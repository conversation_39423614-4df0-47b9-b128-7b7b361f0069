package com.trinasolar.integration.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.trinasolar.integration.api.dto.SystemSyncReqDTO;
import com.trinasolar.integration.api.dto.User;
import com.trinasolar.integration.api.entity.*;
import com.trinasolar.integration.api.knowledge.ISpaceProvider;
import com.trinasolar.integration.api.knowledge.SpaceDTO;
import com.trinasolar.integration.api.vo.AppGitlabSaveVO;
import com.trinasolar.integration.constants.ErrorCodeConstants;
import com.trinasolar.integration.constants.GitLabAccessLevel;
import com.trinasolar.integration.constants.RelationTypeConstant;
import com.trinasolar.integration.controller.devops.vo.DevOpsProjectRespVO;
import com.trinasolar.integration.controller.devops.vo.DevOpsProjectSaveReqVO;
import com.trinasolar.integration.controller.devops.vo.GitGroupRespVO;
import com.trinasolar.integration.dao.AppSyncLogMapper;
import com.trinasolar.integration.dao.AppSystemMapper;
import com.trinasolar.integration.dao.ProjectConfigMapper;
import com.trinasolar.integration.dao.UserAppMapper;
import com.trinasolar.integration.service.*;
import com.trinasolar.integration.service.SCAService;
import com.trinasolar.tasc.framework.common.exception.ServiceException;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.trinasolar.integration.util.ProjectConfigConstants.*;


@Service
@Validated
@Slf4j
public class ProjectServiceImpl implements ProjectService {

    @Resource
    private DevOpsProjectService devOpsProjectService;

    @Resource
    private ProjectConfigMapper projectConfigMapper;

    @Resource
    private AppSystemInitializeService appSystemInitializeService;

    @Resource
    private GitService gitService;

    @Resource
    private AppSystemMapper appSystemMapper;

    @Resource
    private UserAppMapper userAppMapper;

    @Resource
    private ISpaceProvider spaceProvider;

    @Resource
    private AppSyncLogMapper appSyncLogMapper;

    @Resource
    private UserService userService;

    @Resource
    private SCAService scaService;

    @Override
    public DevOpsProjectRespVO createProject(AppGitlabSaveVO appSystemVO) {

        //---------------------创建devops产品基本信息----------------------
        AppSystem appSystem = appSystemMapper.selectById(appSystemVO.getId());
        // 创建devops产品
        DevOpsProjectSaveReqVO reqVO = new DevOpsProjectSaveReqVO();
        reqVO.setProjectName(appSystem.getCnName());
        reqVO.setEnglishNameCustom(appSystem.getEnSimpleName());
        List<String> addUserCodesStr = getSystemAdmin(appSystemVO.getId()); //查询devops管理员
        log.info("addUserCodesStr:{}", JSON.toJSONString(addUserCodesStr));
        reqVO.setAdministrator(String.join(",", addUserCodesStr));
        DevOpsProjectRespVO respVO = devOpsProjectService.createProject(reqVO);
        //更新devops信息
        AppSystemInitialize devopsInfo = new AppSystemInitialize(appSystemVO.getId(), respVO.getProjectId(),
                respVO.getProjectName(), RelationTypeConstant.DVEOPS, respVO.getOriginData());
        devopsInfo.setCreatedTime(LocalDateTime.now());
        devopsInfo.setUpdatedTime(LocalDateTime.now());
        appSystemInitializeService.save(devopsInfo);
        //---------------------devops初始化配置-------------------------
        //加入DevOps项目配置信息
        // 保存项目（应用系统）配置信息
        saveProjectConfig(respVO.getOriginData(), appSystem.getId(), appSystem.getBusinessDomain());
        //初始化DevOps项目信息
        doDevOpsInit(respVO, appSystemVO.getId());
        //初始化gitlab项目信息
        doGitInit(appSystem);
        //初始化知识库
        createKnowlege(appSystem);
        // 返回
        return respVO;
    }

    @Override
    public Boolean syncDownStream(List<SystemSyncReqDTO> systemSyncReqDTOList) {
        //获取当前用户信息
        User user = userService.getUser();
        //依次同步下游系统
        for (SystemSyncReqDTO systemSyncReqDTO : systemSyncReqDTOList) {
            Long systemId = systemSyncReqDTO.getSystemId();
            AppSystem appSystem = appSystemMapper.selectById(systemId);
            if (appSystem == null) {
                log.error("同步下游系统{}失败，应用系统不存在，systemId:{}", systemSyncReqDTO.getDownStreamSystem(), systemId);
                continue;
            }
            //下游系统业务域使用小写
            appSystem.setBusinessDomain(appSystem.getBusinessDomain().toLowerCase());
            //查询应用系统负责人
            List<UserApp> systemAdminUser = getSystemAdminUser(systemId);
            log.info("应用系统负责人:{}", JSON.toJSONString(systemAdminUser));
            // 关联devops系统：创建devops产品，并初始化配置
            if (RelationTypeConstant.DVEOPS.equals(systemSyncReqDTO.getDownStreamSystem().toLowerCase())) {
                try {
                    // 调用devops创建API，创建devops产品
                    DevOpsProjectSaveReqVO reqVO = new DevOpsProjectSaveReqVO();
                    reqVO.setProjectName(appSystem.getCnName());
                    reqVO.setEnglishNameCustom(appSystem.getEnSimpleName());
                    //应用系统负责人工号
                    List<String> respUserCodesStr = systemAdminUser.stream().map(UserApp::getUserCode).distinct().collect(Collectors.toList());
                    reqVO.setAdministrator(String.join(",", respUserCodesStr));
                    DevOpsProjectRespVO respVO = devOpsProjectService.createProject(reqVO);
                    if (Objects.isNull(respVO)) {
                        log.error("同步devops异常，创建devops项目失败，系统名称:{}", appSystem.getCnName());
                        throw new ServiceException(100110, "同步devops异常，创建devops项目失败:" + appSystem.getCnName());
                    }

                    // 保存项目（应用系统）配置信息
                    saveProjectConfig(respVO.getOriginData(), systemId, appSystem.getBusinessDomain());

                    //保存应用系统同步信息（devops）
                    saveDevopsAppSystemInitInfo(systemId, respVO);

                    //初始化devops信息（添加CICD的插件，添加环境组信息，设置制品库的信息，关联所有的凭证，创建git group，应用系统更新git信息）
                    doDevOpsInit(respVO, systemId);

                    //记录同步日志
                    recordAppSyncLog(systemId, appSystem, user, RelationTypeConstant.DVEOPS, "成功");
                } catch (Exception e) {
                    log.error("同步devops异常", e);
                    recordAppSyncLog(systemId, appSystem, user, RelationTypeConstant.DVEOPS, "失败");
                }

            }

            // 关联gitlab：创建gitlab组
            if (RelationTypeConstant.GITLAB.equals(systemSyncReqDTO.getDownStreamSystem().toLowerCase())) {
                //初始化gitlab项目信息
                String syncStatus = "成功";
                try {
                    doGitInit(appSystem);
                } catch (Exception ex) {
                    log.error("同步gitlab异常", ex);
                    syncStatus = "失败";
                } finally {
                    recordAppSyncLog(systemId, appSystem, user, RelationTypeConstant.GITLAB, syncStatus);
                }

            }

            //关联sca
            if (RelationTypeConstant.SCA.equals(systemSyncReqDTO.getDownStreamSystem().toLowerCase())) {
                //初始化sca项目信息
                String syncStatus = "成功";
                try {
                    doScaInit(appSystem, systemAdminUser);
                } catch (Exception ex) {
                    log.error("同步gitlab异常", ex);
                    syncStatus = "失败";
                } finally {
                    recordAppSyncLog(systemId, appSystem, user, RelationTypeConstant.SCA, syncStatus);
                }
            }
        }
        return true;
    }

    private void saveDevopsAppSystemInitInfo(Long systemId, DevOpsProjectRespVO respVO) {
        AppSystemInitialize devopsInfo = new AppSystemInitialize(systemId, respVO.getProjectId(),
                respVO.getProjectName(), RelationTypeConstant.DVEOPS, respVO.getOriginData());
        devopsInfo.setCreatedTime(LocalDateTime.now());
        devopsInfo.setUpdatedTime(LocalDateTime.now());
        appSystemInitializeService.save(devopsInfo);
    }

    /**
     * 保存项目（应用系统）配置信息
     */
    private void saveProjectConfig(String originData, Long systemId, String busDomain) {
        //---------------------devops初始化配置-------------------------
        //加入DevOps项目配置信息
        ProjectConfigDO configDO = new ProjectConfigDO();
        configDO.setConfigContent(originData);
        configDO.setProjectId(systemId);
        configDO.setConfigKey(DEVOPS_BASIC_INFO_KEY);
        configDO.setConfigName("BizDevOps基础信息");
        configDO.setStatus(0);
        configDO.setIsDefault(1);
        projectConfigMapper.insert(configDO);
        //加入项目配置信息
        ProjectConfigDO basicConfigDO = new ProjectConfigDO();
        Map<String, String> projectConfigJson = Maps.newHashMap();
        //k8s namespace，实际替换流水线json文件中[TEAM_NAME]，这里用甲方确认使用业务域替换TEAM_NAME，
        //完整namespace规则为teamname--projectname--{env}，devops会自动拼接并创建namespace
        projectConfigJson.put("namespace", busDomain);
        basicConfigDO.setConfigContent(JSONUtil.toJsonStr(projectConfigJson));
        basicConfigDO.setProjectId(systemId);
        basicConfigDO.setConfigKey(PROJECT_BASIC_INFO_KEY);
        basicConfigDO.setConfigName("项目基础信息");
        basicConfigDO.setStatus(0);
        basicConfigDO.setIsDefault(1);
        projectConfigMapper.insert(basicConfigDO);
    }

    /**
     * 获取devops管理员
     *
     * @param systemId 应用系统ID
     * @return
     */
    @Override
    public List<String> getSystemAdmin(Long systemId) {
        List<UserApp> userApps = userAppMapper.getUserAppByAppId(systemId);
        return userApps.stream().map(UserApp::getUserCode).distinct().collect(Collectors.toList());
    }

    @Override
    public List<UserApp> getSystemAdminUser(Long systemId) {
        return userAppMapper.getUserAppByAppId(systemId);
    }

    /**
     * 初始化devops信息（添加CICD的插件，添加环境组信息，设置制品库的信息，关联所有的凭证，创建git group，应用系统更新git信息）
     *
     * @param respVO
     * @param systemId
     */
    @Async
    public void doDevOpsInit(DevOpsProjectRespVO respVO, Long systemId) {
        //添加CICD的插件
        devOpsProjectService.addCicdPlugins(respVO.getProjectId());
        //添加环境组信息
        Map<String, String> envs = devOpsProjectService.addEnvGroup(respVO.getProjectId());
        addProjectEnvConfigs(envs, systemId);
        //设置制品库的信息
        devOpsProjectService.enableDockerImgRepoPublic(respVO.getProjectId());
        //关联所有的凭证
        devOpsProjectService.relatedAllCredentials(respVO.getProjectId());
    }

    /**
     * 初始化gitlab信息:创建git group,更新AppSystemInitialize同步信息
     *
     * @param appSystem
     */
    @Async
    public void doGitInit(AppSystem appSystem) {
        //创建git group
        String namespace = appSystem.getEnSimpleName();
        String businessDomain = appSystem.getBusinessDomain();
        GitGroupRespVO gitGroup = gitService.createGitGroup(namespace, businessDomain);
        List<String> addUserNames = getSystemAdmin(appSystem.getId());
        gitService.addUserToGitGroup(businessDomain, namespace, addUserNames, GitLabAccessLevel.OWNER);
        //应用系统更新git信息
        AppSystemInitialize devopsInfo = new AppSystemInitialize(appSystem.getId(), String.valueOf(gitGroup.getId()),
                gitGroup.getName(), RelationTypeConstant.GITLAB, JSONUtil.toJsonStr(gitGroup));
        devopsInfo.setCreatedTime(LocalDateTime.now());
        devopsInfo.setUpdatedTime(LocalDateTime.now());
        appSystemInitializeService.save(devopsInfo);
    }

    public void doScaInit(AppSystem appSystem, List<UserApp> userApps) {
        Integer projectId = scaService.createProjects(appSystem.getCnName(), userApps);
        boolean exists = appSystemInitializeService.exists(new LambdaQueryWrapper<AppSystemInitialize>()
                .eq(AppSystemInitialize::getAppId, appSystem.getId())
                .eq(AppSystemInitialize::getRelationType, RelationTypeConstant.SCA)
                .eq(AppSystemInitialize::getRelationId, String.valueOf(projectId))
                .eq(AppSystemInitialize::getRelationName, appSystem.getCnName()));
        if (exists) {
            log.info("sca项目已存在，无需重复创建，appSystemId:{},projectId:{},projectName:{}", appSystem.getId(), projectId, appSystem.getCnName());
            return;
        }
        //应用系统更新sca信息
        AppSystemInitialize devopsInfo = new AppSystemInitialize(appSystem.getId(), String.valueOf(projectId),
                appSystem.getCnName(), RelationTypeConstant.SCA, JSONUtil.toJsonStr(projectId));
        devopsInfo.setCreatedTime(LocalDateTime.now());
        devopsInfo.setUpdatedTime(LocalDateTime.now());
        appSystemInitializeService.save(devopsInfo);
    }


    private void addProjectEnvConfigs(Map<String, String> envs, Long systemId) {
        ProjectConfigDO basicConfigDO = new ProjectConfigDO();
        basicConfigDO.setConfigContent(JSONUtil.toJsonStr(envs));
        basicConfigDO.setProjectId(systemId);
        basicConfigDO.setConfigKey(PROJECT_DEVOPS_ENV_INFO_KEY);
        basicConfigDO.setConfigName("项目流水线环境信息");
        basicConfigDO.setStatus(0);
        basicConfigDO.setIsDefault(1);
        projectConfigMapper.insert(basicConfigDO);
    }

    @Override
    public CommonResult<Long> createKnowlege(Long systemId) {
        AppSystem appSystem = appSystemMapper.selectById(systemId);
        if (appSystem == null) {
            log.error("创建知识库失败，应用系统不存在，systemId:{}", systemId);
            return CommonResult.error(ErrorCodeConstants.APP_NOT_EXISTS);
        }
        return createKnowlege(appSystem);
    }

    @Async
    public CommonResult<Long> createKnowlege(AppSystem appSystem) {
        SpaceDTO spaceDTO = new SpaceDTO();
        spaceDTO.setName(appSystem.getCnName());
        spaceDTO.setDescription(appSystem.getEnName());
        spaceDTO.setProjectId(appSystem.getId());
        spaceDTO.setSpaceType(1);
        CommonResult<Long> longCommonResult = spaceProvider.create(spaceDTO);
        log.info("createKnowlege space result:{}", longCommonResult);
        return longCommonResult;
    }

    /**
     * 记录同步日志
     */
    private void recordAppSyncLog(Long systemId, AppSystem appSystem, User user, String downStreamName, String syncStatus) {
        AppSyncLog appSyncLog = new AppSyncLog();
        appSyncLog.setAppId(systemId);
        appSyncLog.setAppName(appSystem.getCnName());
        appSyncLog.setDownStreamName(downStreamName);
        appSyncLog.setCreatorId(Long.valueOf(user.getId()));
        appSyncLog.setCreatorName(user.getUserRealname());
        appSyncLog.setCreatedTime(LocalDateTime.now());
        appSyncLog.setSyncStatus(syncStatus);
        appSyncLogMapper.insert(appSyncLog);
    }
}