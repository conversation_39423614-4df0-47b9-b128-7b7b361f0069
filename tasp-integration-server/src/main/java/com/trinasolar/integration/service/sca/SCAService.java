package com.trinasolar.integration.service.sca;

import com.alibaba.fastjson.JSONObject;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface SCAService {
    /**
     * 生成并维护签名参数
     *
     * @param payloadObject 请求体
     * @param requestParams 请求入场
     * @return 响应是入参请求头参数
     */
    Map<String, String> generateSignatureParams(JSONObject payloadObject, Map<String, String> requestParams);


}
