package com.trinasolar.integration.service.appmarket;

import com.alibaba.fastjson.JSONObject;
import com.trinasolar.integration.api.dto.*;
import com.trinasolar.integration.dto.apimarket.ApiDetailInfo;
import com.trinasolar.integration.dto.apimarket.UserDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ApiMarketService {

    String createToken();

    UserDTO checkToken(String token);

    PageResultDTO getApiCardList(String name, String apiPath, String pubOrgName,
                                 String startTime, String endTime, String desc,
                                 Integer pageSize, Integer pageNo, Integer tagId);

    ApiDetailInfo getDetail(Long id);

    List<TagDTO> getTags();


    List<OrgDTO> getSubOrg(Long pubOrgId);

    Boolean subscribeApi(SubscribeApiDTO subscribeApiDTO);

    List<String> getApproveUsers(Long id);

    ApiApproveDTO rollback(ApiRollbackDTO apiRollbackDTO);

    SubLogDTO getSubLogs(String name, String requestBody, String responseBody,
                         String responseStatus, String startTime, String endTime,
                         Integer pageSize, Integer pageNo);

    LogDetailDTO getSubLogDetail(String eventId);


    OrderPage getMyOrderPage(String productName, String apiPath, String pubOrgName, String startTime, String endTime, Integer pageSize, Integer pageNo);

    Map<String, Object> sendApi(SendApiDTO sendApiDTO);

    Boolean apiPub(JSONObject jsonObject);

    ApiApproveDTO pubRollback(ApiRollbackDTO apiRollbackDTO);
}


