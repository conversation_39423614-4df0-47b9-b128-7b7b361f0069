package com.trinasolar.integration.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.integration.api.dto.CategoryDocmentDTO;
import com.trinasolar.integration.api.dto.User;
import com.trinasolar.integration.api.entity.ShareComponentDO;
import com.trinasolar.integration.api.knowledge.ISpaceProvider;
import com.trinasolar.integration.api.resq.Categories;
import com.trinasolar.integration.api.resq.R;
import com.trinasolar.integration.api.service.ProductService;
import com.trinasolar.integration.controller.component.ShareComponentBO;
import com.trinasolar.integration.controller.component.ShareComponentReq;
import com.trinasolar.integration.controller.component.ShareComponentVO;
import com.trinasolar.integration.dao.ShareComponentMapper;
import com.trinasolar.integration.service.ShareComponentService;
import com.trinasolar.integration.util.RequestUtils;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ShareComponentServiceImpl extends ServiceImpl<ShareComponentMapper, ShareComponentDO> implements ShareComponentService {

    @Autowired
    private ShareComponentMapper shareComponentMapper;

    @Autowired
    private ProductService productService;

    @Autowired
    private ISpaceProvider keplerKnowledgeClient;

    @Override
    public Page<ShareComponentVO> shareComponentPage(Page<ShareComponentVO> page, ShareComponentReq req) {
        page.setSize(req.getSize());
        page.setCurrent(req.getPage());
        page.setOptimizeCountSql(false);

        return shareComponentMapper.shareComponentPage(page, req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addShareComponent(ShareComponentBO shareComponentBO) {
        LocalDateTime now = LocalDateTime.now();
        User currentUser = RequestUtils.getCurrentUser();
        if (Objects.nonNull(currentUser)) {
            shareComponentBO.setCreator(currentUser.getUserRealname());
            shareComponentBO.setUpdater(currentUser.getUserRealname());
        }
        shareComponentBO.setUpdateTime(now);
        shareComponentBO.setCreateTime(now);
        shareComponentBO.setDeleted(0);
        shareComponentBO.setStatus(0);
        if (StringUtils.isNotEmpty(shareComponentBO.getFileName()) && shareComponentBO.getFileName().contains(".")){
            String fileName = shareComponentBO.getFileName();
            shareComponentBO.setFileType(fileName.substring(fileName.lastIndexOf(".") + 1));
        }
        ShareComponentDO shareComponentDO = BeanUtil.copyProperties(shareComponentBO, ShareComponentDO.class);
        int row = shareComponentMapper.insert(shareComponentDO);
        if (row > 0 && Objects.equals(shareComponentDO.getStatus(), 0)) {
            // 新增组件成功后，将组件添加到产品中
            productService.addComponentToProduct(shareComponentDO);
            // 添加组件成功后，将共享组件相关文档同步到技术文档库中
            this.syncComponentDoc(shareComponentDO);
            return true;
        }
        return false;
    }

    private void syncComponentDoc(ShareComponentDO shareComponentDO) {
        try {
            // 先指定目录id
            CategoryDocmentDTO document = new CategoryDocmentDTO();
            R<Long> result = keplerKnowledgeClient.getCatalog();
            log.info("共享组件-目录id:{}", result);
            if (Objects.nonNull(result) && Objects.nonNull(result.getData())) {
                document.setCategoriesIds(String.valueOf(result.getData()));
                document.setAlias(shareComponentDO.getComponentName());
                document.setDescription(shareComponentDO.getDescription());
                document.setIsRelease(1);
                document.setKeyWords(shareComponentDO.getComponentName());
                document.setDocumentId(shareComponentDO.getFileId());
                keplerKnowledgeClient.add(document);
            }
        }catch (Exception e) {
            log.warn("同步组件文档失败,共享组件信息:{}", JSON.toJSON(shareComponentDO), e);
        }
    }

    @Override
    public boolean checkComponentNameExists(String componentName, Long id) {
        int row = shareComponentMapper.checkComponentNameExists(componentName, id);
        if (row > 0) {
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateShareComponent(ShareComponentBO shareComponentBO) {
        ShareComponentDO shareComponent = shareComponentMapper.selectById(shareComponentBO.getId());
        if (Objects.isNull(shareComponent)) {
            return false;
        }
        LocalDateTime now = LocalDateTime.now();
        User currentUser = RequestUtils.getCurrentUser();
        if (Objects.nonNull(currentUser)) {
            shareComponentBO.setUpdater(currentUser.getUserRealname());
        }
        shareComponentBO.setUpdateTime(now);
        if (StringUtils.isNotEmpty(shareComponentBO.getFileName()) && shareComponentBO.getFileName().contains(".")){
            String fileName = shareComponentBO.getFileName();
            shareComponentBO.setFileType(fileName.substring(fileName.lastIndexOf(".") + 1));
        }
        ShareComponentDO shareComponentDO = BeanUtil.copyProperties(shareComponentBO, ShareComponentDO.class);
        int row = shareComponentMapper.update(shareComponentDO, new UpdateWrapper<ShareComponentDO>().eq("id", shareComponentBO.getId()));
        if (row > 0) {
            if (Objects.equals(shareComponentDO.getStatus(), 1)) {
                productService.deleteComponentToProduct(shareComponentBO.getId());
            }else {
                // 更新组件成功后，将组件添加到产品中
                productService.updateComponentToProduct(shareComponentDO);
                if (!Objects.equals(shareComponent.getFileId(), shareComponentBO.getFileId())){
                    // 添加组件成功后，将共享组件相关文档同步到技术文档库中
                    this.syncComponentDoc(shareComponentDO);
                }
            }
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteShareComponent(Long id) {
        ShareComponentDO shareComponentDO = shareComponentMapper.selectById(id);
        if (Objects.nonNull(shareComponentDO)) {
            User currentUser = RequestUtils.getCurrentUser();
            if (Objects.nonNull(currentUser)) {
                shareComponentDO.setUpdater(currentUser.getUserRealname());
            }
            shareComponentDO.setUpdateTime(LocalDateTime.now());
            shareComponentDO.setDeleted(1);
            int row = shareComponentMapper.updateById(shareComponentDO);
            if (row > 0) {
                // 删除组件成功后，将组件移除产品列表
                productService.deleteComponentToProduct(shareComponentDO.getId());
                return true;
            }
        }
        return false;
    }

    @Override
    public Boolean updateStatus(Long id, Integer status) {
         if (shareComponentMapper.updateStatus(id, status) > 0){
             if (Objects.equals(status, 1)) {
                 // 删除组件成功后，将组件移除产品列表
                 productService.deleteComponentToProduct(id);
             }else {
                 // 添加组件成功后，将组件添加到产品列表
                 productService.addComponentToProduct(shareComponentMapper.selectById(id));
             }
             return true;
         }
         return false;
    }
}

