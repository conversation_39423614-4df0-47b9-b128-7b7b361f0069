package com.trinasolar.integration.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.integration.api.entity.ProductServicePO;
import com.trinasolar.integration.api.entity.ShareComponentDO;
import com.trinasolar.integration.api.mapper.ProductServiceMapper;
import com.trinasolar.integration.api.service.ProductService;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class ProductServiceImpl extends ServiceImpl<ProductServiceMapper, ProductServicePO> implements ProductService {
    @Override
    public List<ProductServicePO> getAllProducts() {
        return list();
    }

    @Override
    public int addComponentToProduct(ShareComponentDO shareComponentDO) {
        ProductServicePO productServicePO = new ProductServicePO();
        productServicePO.setName(shareComponentDO.getComponentName());
        productServicePO.setDescription(shareComponentDO.getDescription());
        productServicePO.setUrl(shareComponentDO.getFileUrl());
        productServicePO.setLogoUrl(shareComponentDO.getLogoUrl());
        productServicePO.setType("共享组件");
        productServicePO.setCategory("共享组件");
        productServicePO.setVersion(shareComponentDO.getVersion());
        productServicePO.setComponentId(shareComponentDO.getId());
        productServicePO.setUpdater(shareComponentDO.getUpdater());
        productServicePO.setCreator(shareComponentDO.getCreator());
        productServicePO.setCreateTime(shareComponentDO.getCreateTime());
        productServicePO.setUpdateTime(shareComponentDO.getUpdateTime());
        productServicePO.setFileType(shareComponentDO.getFileType());
        return save(productServicePO) ? 1 : 0;
    }

    @Override
    public int updateComponentToProduct(ShareComponentDO shareComponentDO) {
        ProductServicePO productServicePO = baseMapper.selectOne(new QueryWrapper<ProductServicePO>().eq("component_id", shareComponentDO.getId()));
        if (Objects.nonNull(productServicePO)) {
            productServicePO.setName(shareComponentDO.getComponentName());
            productServicePO.setDescription(shareComponentDO.getDescription());
            productServicePO.setUrl(shareComponentDO.getFileUrl());
            productServicePO.setLogoUrl(shareComponentDO.getLogoUrl());
            productServicePO.setVersion(shareComponentDO.getVersion());
            return updateById(productServicePO) ? 1 : 0;
        }
        return 0;
    }

    @Override
    public int deleteComponentToProduct(Long id) {
        return baseMapper.delete(new QueryWrapper<ProductServicePO>().eq("component_id", id));
    }
}
