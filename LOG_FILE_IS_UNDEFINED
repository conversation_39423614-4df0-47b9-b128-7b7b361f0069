2025-09-03 14:23:04.069 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-03 14:23:04.246 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-03 14:23:04.248 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 14:23:04.249 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 14:23:04.249 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 14:23:04.250 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-03 14:23:04.250 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-03 14:23:04.251 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 14:23:04.251 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 14:23:04.320 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-03 14:23:04.328 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-03 14:23:04.330 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-03 14:23:04.790 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m | LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2025-09-03 14:23:04.816 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.utils.JvmUtil   [0;39m | isMultiInstance:false
2025-09-03 14:23:04.843 | [31m WARN 52975[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m | Ignore the empty nacos configuration and get it based on dataId[kepler-integration] & group[DEFAULT_GROUP]
2025-09-03 14:23:04.858 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mb.c.PropertySourceBootstrapConfiguration[0;39m | Located property source: [BootstrapPropertySource {name='bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kepler-integration,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-application.yml,DEFAULT_GROUP'}]
2025-09-03 14:23:04.890 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mEnableEncryptablePropertiesConfiguration[0;39m | Bootstraping jasypt-string-boot auto configuration in context: kepler-integration-1
2025-09-03 14:23:04.891 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | The following 2 profiles are active: "dev", "uat"
2025-09-03 14:23:06.261 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-03 14:23:06.263 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-03 14:23:06.324 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32m.s.d.r.c.RepositoryConfigurationDelegate[0;39m | Finished Spring Data repository scanning in 41 ms. Found 0 Redis repository interfaces.
2025-09-03 14:23:06.647 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mo.s.cloud.context.scope.GenericScope    [0;39m | BeanFactory id=33324d0a-f995-3cc9-b857-b14a8953aa71
2025-09-03 14:23:06.707 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mptablePropertiesBeanFactoryPostProcessor[0;39m | Post-processing PropertySource instances
2025-09-03 14:23:06.721 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-03 14:23:06.722 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-kepler-integration,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-03 14:23:06.722 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource bootstrapProperties-application.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-09-03 14:23:06.722 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-09-03 14:23:06.722 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-03 14:23:06.723 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-09-03 14:23:06.723 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 14:23:06.723 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 14:23:06.723 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-09-03 14:23:06.723 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-09-03 14:23:06.723 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 14:23:06.723 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.EncryptablePropertySourceConverter[0;39m | Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-09-03 14:23:06.734 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m | Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-09-03 14:23:06.743 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.r.DefaultLazyPropertyResolver     [0;39m | Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-09-03 14:23:06.744 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.d.DefaultLazyPropertyDetector     [0;39m | Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-09-03 14:23:06.910 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 14:23:06.913 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 14:23:06.915 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$658/0x00000008005a1c40] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 14:23:06.917 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mtrationDelegate$BeanPostProcessorChecker[0;39m | Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 14:23:07.362 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat initialized with port(s): 80 (http)
2025-09-03 14:23:07.373 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mo.apache.catalina.core.StandardService  [0;39m | Starting service [Tomcat]
2025-09-03 14:23:07.373 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32morg.apache.catalina.core.StandardEngine [0;39m | Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-03 14:23:07.512 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mo.a.c.c.C.[.[.[/kepler/integration]     [0;39m | Initializing Spring embedded WebApplicationContext
2025-09-03 14:23:07.512 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mw.s.c.ServletWebServerApplicationContext[0;39m | Root WebApplicationContext: initialization completed in 2601 ms
2025-09-03 14:23:07.847 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mf.a.AutowiredAnnotationBeanPostProcessor[0;39m | Autowired annotation is not supported on static fields: private static java.lang.String com.trinasolar.integration.config.security.aes.AESConfig.key
2025-09-03 14:23:07.913 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join properties config complete
2025-09-03 14:23:08.350 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1,master} inited
2025-09-03 14:23:08.351 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource - add a datasource named [master] success
2025-09-03 14:23:08.351 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-03 14:23:08.413 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m | mybatis plus join SqlInjector init
2025-09-03 14:23:10.714 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | String Encryptor custom Bean not found with name 'jasyptStringEncryptor'. Initializing Default String Encryptor
2025-09-03 14:23:10.722 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.algorithm, using default value: PBEWithMD5AndDES
2025-09-03 14:23:10.722 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.keyObtentionIterations, using default value: 1000
2025-09-03 14:23:10.722 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.poolSize, using default value: 1
2025-09-03 14:23:10.722 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerName, using default value: null
2025-09-03 14:23:10.722 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.providerClassName, using default value: null
2025-09-03 14:23:10.723 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.saltGeneratorClassname, using default value: org.jasypt.salt.RandomSaltGenerator
2025-09-03 14:23:10.723 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.ivGeneratorClassname, using default value: org.jasypt.salt.NoOpIVGenerator
2025-09-03 14:23:10.724 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.u.j.encryptor.DefaultLazyEncryptor    [0;39m | Encryptor config not found for property jasypt.encryptor.stringOutputType, using default value: base64
2025-09-03 14:23:10.985 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32morg.redisson.Version                    [0;39m | Redisson 3.41.0
2025-09-03 14:23:11.066 | [31m WARN 52975[0;39m | [1;33mmain[0;39m [1;32mi.n.r.d.DnsServerAddressStreamProviders [0;39m | Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-09-03 14:23:11.324 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | Redis cluster nodes configuration got from ***********/***********:30012:
461896f00a0c24e3126dc34638854ff6422fb244 ***********:30012@30018 myself,master - 0 1756880588000 1 connected 0-5460
e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 ***********:30014@30020 master - 0 1756880589832 3 connected 10923-16383
93167ed470a2aaf64573d363e6bd6554e03bc4d9 ***********:30013@30019 master - 0 1756880590835 2 connected 5461-10922
0d5fd0961bdeb1f6249808fe9218ccbf5707f630 ***********:30017@30023 slave 461896f00a0c24e3126dc34638854ff6422fb244 0 1756880588827 1 connected
7814d1a5f8f3a16c9b5a1a4212329b85b44e2027 ***********:30015@30021 slave 93167ed470a2aaf64573d363e6bd6554e03bc4d9 0 1756880589000 2 connected
19e5162fb0b4f5bd64d046f5a9a2c2b1db62918e ***********:30016@30022 slave e0b782a8e69537dcc37ea3d2a72258a45ddb2d53 0 1756880588000 3 connected

2025-09-03 14:23:11.373 | [34m INFO 52975[0;39m | [1;33mredisson-netty-1-16[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30012
2025-09-03 14:23:11.406 | [34m INFO 52975[0;39m | [1;33mredisson-netty-1-21[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30014
2025-09-03 14:23:11.435 | [34m INFO 52975[0;39m | [1;33mredisson-netty-1-26[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30013
2025-09-03 14:23:11.893 | [34m INFO 52975[0;39m | [1;33mredisson-netty-1-20[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30013
2025-09-03 14:23:11.931 | [34m INFO 52975[0;39m | [1;33mredisson-netty-1-28[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30015
2025-09-03 14:23:12.080 | [34m INFO 52975[0;39m | [1;33mredisson-netty-1-4[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30012
2025-09-03 14:23:12.121 | [34m INFO 52975[0;39m | [1;33mredisson-netty-1-11[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30017
2025-09-03 14:23:12.375 | [34m INFO 52975[0;39m | [1;33mredisson-netty-1-32[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30015
2025-09-03 14:23:12.375 | [34m INFO 52975[0;39m | [1;33mredisson-netty-1-14[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30014
2025-09-03 14:23:12.375 | [34m INFO 52975[0;39m | [1;33mredisson-netty-1-32[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30015] added for master: redis://***********:30013 slot ranges: [[5461-10922]]
2025-09-03 14:23:12.376 | [34m INFO 52975[0;39m | [1;33mredisson-netty-1-32[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30013 added for slot ranges: [[5461-10922]]
2025-09-03 14:23:12.422 | [34m INFO 52975[0;39m | [1;33mredisson-netty-1-26[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 1 connections initialized for ***********/***********:30016
2025-09-03 14:23:12.561 | [34m INFO 52975[0;39m | [1;33mredisson-netty-1-1[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30017
2025-09-03 14:23:12.561 | [34m INFO 52975[0;39m | [1;33mredisson-netty-1-1[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30017] added for master: redis://***********:30012 slot ranges: [[0-5460]]
2025-09-03 14:23:12.562 | [34m INFO 52975[0;39m | [1;33mredisson-netty-1-1[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30012 added for slot ranges: [[0-5460]]
2025-09-03 14:23:14.642 | [34m INFO 52975[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.redisson.connection.ConnectionsHolder [0;39m | 24 connections initialized for ***********/***********:30016
2025-09-03 14:23:14.643 | [34m INFO 52975[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | slaves: [redis://***********:30016] added for master: redis://***********:30014 slot ranges: [[10923-16383]]
2025-09-03 14:23:14.643 | [34m INFO 52975[0;39m | [1;33mredisson-netty-1-22[0;39m [1;32mo.r.connection.ClusterConnectionManager [0;39m | master: redis://***********:30014 added for slot ranges: [[10923-16383]]
2025-09-03 14:23:15.604 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.t.t.f.j.c.TsJacksonAutoConfiguration  [0;39m | [init][初始化 JsonUtils 成功]
2025-09-03 14:23:16.037 | [31m WARN 52975[0;39m | [1;33mmain[0;39m [1;32miguration$LoadBalancerCaffeineWarnLogger[0;39m | Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-03 14:23:16.211 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-03 14:23:16.212 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Environment :null
2025-09-03 14:23:16.212 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | initializer namespace from System Property :null
2025-09-03 14:23:16.475 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"*************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"*************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-03 14:23:16.483 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"*************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"*************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-03 14:23:16.505 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m | Tomcat started on port(s): 80 (http) with context path '/kepler/integration'
2025-09-03 14:23:16.510 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] adding beat: BeatInfo{port=80, ip='************', weight=1.0, serviceName='DEFAULT_GROUP@@kepler-integration', cluster='DEFAULT', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}, scheduled=false, period=5000, stopped=false} to beat map.
2025-09-03 14:23:16.511 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [REGISTER-SERVICE] dev registering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.heart.beat.timeout=15000, preserved.ip.delete.timeout=30000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=5000}}
2025-09-03 14:23:16.519 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | nacos registry, DEFAULT_GROUP kepler-integration ************:80 register finished
2025-09-03 14:23:16.536 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.t.i.KeplerIntegrationServerApplication[0;39m | Started KeplerIntegrationServerApplication in 14.981 seconds (JVM running for 16.463)
2025-09-03 14:23:16.538 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始执行数据初始化任务...
2025-09-03 14:23:16.578 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 获取到初始化锁，开始执行数据初始化...
2025-09-03 14:23:16.578 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 开始检查应用系统变更表初始化状态...
2025-09-03 14:23:16.738 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 查询到应用系统数据 69 条，开始全量对比变更表...
2025-09-03 14:23:16.817 | [1;31mERROR 52975[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 数据初始化任务执行失败

org.springframework.jdbc.UncategorizedSQLException: Error attempting to get column 'system_code' from result set.  Cause: java.sql.SQLException: Error
; uncategorized SQLException; SQL state [null]; error code [0]; Error; nested exception is java.sql.SQLException: Error
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy118.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy174.selectList(Unknown Source)
	at com.trinasolar.integration.task.DataInitializationTask.initializeAppSystemChangeTable(DataInitializationTask.java:113)
	at com.trinasolar.integration.task.DataInitializationTask.run(DataInitializationTask.java:71)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:485)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:474)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:497)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.trinasolar.integration.KeplerIntegrationServerApplication.main(KeplerIntegrationServerApplication.java:20)
Caused by: java.sql.SQLException: Error
	at com.alibaba.druid.pool.DruidDataSource.handleConnectionException(DruidDataSource.java:1609)
	at com.alibaba.druid.pool.DruidPooledConnection.handleException(DruidPooledConnection.java:124)
	at com.alibaba.druid.pool.DruidPooledStatement.checkException(DruidPooledStatement.java:87)
	at com.alibaba.druid.pool.DruidPooledResultSet.checkException(DruidPooledResultSet.java:42)
	at com.alibaba.druid.pool.DruidPooledResultSet.getInt(DruidPooledResultSet.java:282)
	at org.apache.ibatis.type.IntegerTypeHandler.getNullableResult(IntegerTypeHandler.java:36)
	at org.apache.ibatis.type.IntegerTypeHandler.getNullableResult(IntegerTypeHandler.java:26)
	at org.apache.ibatis.type.BaseTypeHandler.getResult(BaseTypeHandler.java:86)
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.applyColumnOrderBasedConstructorAutomapping(DefaultResultSetHandler.java:790)
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.applyConstructorAutomapping(DefaultResultSetHandler.java:776)
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.createByConstructorSignature(DefaultResultSetHandler.java:727)
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.createResultObject(DefaultResultSetHandler.java:689)
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.createResultObject(DefaultResultSetHandler.java:659)
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.getRowValue(DefaultResultSetHandler.java:411)
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValuesForSimpleResultMap(DefaultResultSetHandler.java:366)
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleRowValues(DefaultResultSetHandler.java:337)
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSet(DefaultResultSetHandler.java:310)
	at org.apache.ibatis.executor.resultset.DefaultResultSetHandler.handleResultSets(DefaultResultSetHandler.java:202)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:66)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy201.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy199.query(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.github.yulichang.interceptor.MPJInterceptor.intercept(MPJInterceptor.java:93)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at com.sun.proxy.$Proxy199.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 24 common frames omitted
Caused by: java.lang.NumberFormatException: For input string: "de569c25-5c8e-4fcf-ae68-954dadcc28f2"
	at java.base/jdk.internal.math.FloatingDecimal.readJavaFormatString(FloatingDecimal.java:2054)
	at java.base/jdk.internal.math.FloatingDecimal.parseDouble(FloatingDecimal.java:110)
	at java.base/java.lang.Double.parseDouble(Double.java:543)
	at com.mysql.cj.protocol.a.MysqlTextValueDecoder.getDouble(MysqlTextValueDecoder.java:249)
	at com.mysql.cj.result.AbstractNumericValueFactory.createFromBytes(AbstractNumericValueFactory.java:56)
	at com.mysql.cj.protocol.a.MysqlTextValueDecoder.decodeByteArray(MysqlTextValueDecoder.java:143)
	at com.mysql.cj.protocol.result.AbstractResultsetRow.decodeAndCreateReturnValue(AbstractResultsetRow.java:135)
	at com.mysql.cj.protocol.result.AbstractResultsetRow.getValueFromBytes(AbstractResultsetRow.java:243)
	at com.mysql.cj.protocol.a.result.TextBufferRow.getValue(TextBufferRow.java:132)
	at com.mysql.cj.jdbc.result.ResultSetImpl.getObject(ResultSetImpl.java:1324)
	at com.mysql.cj.jdbc.result.ResultSetImpl.getInt(ResultSetImpl.java:830)
	at com.mysql.cj.jdbc.result.ResultSetImpl.getInt(ResultSetImpl.java:851)
	at com.alibaba.druid.filter.FilterChainImpl.resultSet_getInt(FilterChainImpl.java:1150)
	at com.alibaba.druid.filter.FilterAdapter.resultSet_getInt(FilterAdapter.java:1630)
	at com.alibaba.druid.filter.FilterChainImpl.resultSet_getInt(FilterChainImpl.java:1146)
	at com.alibaba.druid.proxy.jdbc.ResultSetProxyImpl.getInt(ResultSetProxyImpl.java:477)
	at com.alibaba.druid.pool.DruidPooledResultSet.getInt(DruidPooledResultSet.java:280)
	... 68 common frames omitted

2025-09-03 14:23:16.838 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.t.i.task.DataInitializationTask       [0;39m | 释放初始化锁
2025-09-03 14:23:16.850 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration+DEFAULT_GROUP+dev
2025-09-03 14:23:16.851 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration, group=DEFAULT_GROUP, cnt=1
2025-09-03 14:23:16.852 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.a.n.client.config.impl.ClientWorker   [0;39m | [fixed-10.180.72.6_8848-dev] [subscribe] kepler-integration.yaml+DEFAULT_GROUP+dev
2025-09-03 14:23:16.852 | [34m INFO 52975[0;39m | [1;33mmain[0;39m [1;32mc.a.nacos.client.config.impl.CacheData  [0;39m | [fixed-10.180.72.6_8848-dev] [add-listener] ok, tenant=dev, dataId=kepler-integration.yaml, group=DEFAULT_GROUP, cnt=1
2025-09-03 14:23:17.533 | [34m INFO 52975[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | new ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-03 14:23:17.534 | [34m INFO 52975[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(2) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000},{"instanceId":"*************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"*************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-03 14:24:33.582 | [31m WARN 52975[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-03 14:24:33.582 | [31m WARN 52975[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Start destroying Publisher
2025-09-03 14:24:33.583 | [31m WARN 52975[0;39m | [1;33mThread-16[0;39m [1;32mc.a.nacos.common.notify.NotifyCenter    [0;39m | [NotifyCenter] Destruction of the end
2025-09-03 14:24:33.585 | [31m WARN 52975[0;39m | [1;33mThread-2[0;39m [1;32mc.a.n.common.http.HttpClientBeanHolder  [0;39m | [HttpClientBeanHolder] Destruction of the end
2025-09-03 14:24:33.617 | [34m INFO 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registering from Nacos Server now...
2025-09-03 14:24:33.617 | [34m INFO 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [BEAT] removing beat: DEFAULT_GROUP@@kepler-integration:************:80 from beat map.
2025-09-03 14:24:33.618 | [34m INFO 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [DEREGISTER-SERVICE] dev deregistering service DEFAULT_GROUP@@kepler-integration with instance: Instance{instanceId='null', ip='************', port=80, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
2025-09-03 14:24:33.630 | [34m INFO 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.c.n.registry.NacosServiceRegistry   [0;39m | De-registration finished.
2025-09-03 14:24:33.630 | [34m INFO 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-09-03 14:24:36.641 | [34m INFO 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-09-03 14:24:36.641 | [34m INFO 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin
2025-09-03 14:24:37.650 | [34m INFO 52975[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | removed ips(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-03 14:24:37.652 | [34m INFO 52975[0;39m | [1;33mcom.alibaba.nacos.client.naming.updater[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | current ips:(1) service: DEFAULT_GROUP@@kepler-integration@@DEFAULT -> [{"instanceId":"*************#80#DEFAULT#DEFAULT_GROUP@@kepler-integration","ip":"*************","port":80,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@kepler-integration","metadata":{"preserved.heart.beat.timeout":"15000","preserved.ip.delete.timeout":"30000","preserved.register.source":"SPRING_CLOUD","preserved.heart.beat.interval":"5000"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-09-03 14:24:37.653 | [34m INFO 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin
2025-09-03 14:24:40.661 | [34m INFO 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop
2025-09-03 14:24:40.661 | [34m INFO 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-09-03 14:24:40.661 | [34m INFO 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-09-03 14:24:40.661 | [34m INFO 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop
2025-09-03 14:24:40.661 | [34m INFO 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin
2025-09-03 14:24:40.661 | [31m WARN 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-09-03 14:24:40.661 | [31m WARN 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | [NamingHttpClientManager] Destruction of the end
2025-09-03 14:24:40.662 | [34m INFO 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialWatcher [0;39m | [null] CredentialWatcher is stopped
2025-09-03 14:24:40.662 | [34m INFO 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.a.n.client.identify.CredentialService [0;39m | [null] CredentialService is freed
2025-09-03 14:24:40.662 | [34m INFO 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.nacos.client.naming         [0;39m | com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop
2025-09-03 14:24:40.662 | [31m WARN 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mo.s.b.f.support.DisposableBeanAdapter   [0;39m | Custom destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception: java.lang.NullPointerException
2025-09-03 14:24:40.702 | [34m INFO 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource start closing ....
2025-09-03 14:24:40.705 | [34m INFO 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closing ...
2025-09-03 14:24:40.713 | [34m INFO 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mcom.alibaba.druid.pool.DruidDataSource  [0;39m | {dataSource-1} closed
2025-09-03 14:24:40.713 | [34m INFO 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.d.DefaultDataSourceDestroyer    [0;39m | dynamic-datasource close the datasource named [master] success,
2025-09-03 14:24:40.714 | [34m INFO 52975[0;39m | [1;33mSpringApplicationShutdownHook[0;39m [1;32mc.b.d.d.DynamicRoutingDataSource        [0;39m | dynamic-datasource all closed success,bye
